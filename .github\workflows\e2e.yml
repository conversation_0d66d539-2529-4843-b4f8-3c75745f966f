# .github/workflows/e2e.yml

name: E2E Tests

on:
  push:
    branches: [main]
  merge_group:

jobs:
  e2e-test:
    name: E2E Test - ${{ matrix.sandbox }}
    runs-on: ubuntu-latest
    strategy:
      matrix:
        sandbox: [sandbox:none, sandbox:docker]
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20.x
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build project
        run: npm run build

      - name: Set up Docker
        if: matrix.sandbox == 'sandbox:docker'
        uses: docker/setup-buildx-action@v3

      - name: Set up Podman
        if: matrix.sandbox == 'sandbox:podman'
        uses: redhat-actions/podman-login@v1
        with:
          registry: docker.io
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Run E2E tests
        env:
          GEMINI_API_KEY: ${{ secrets.GEMINI_API_KEY }}
        run: npm run test:integration:${{ matrix.sandbox }} -- --verbose --keep-output
